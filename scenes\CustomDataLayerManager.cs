using Godot;
using System;
using System.Collections.Generic;
using System.Text.Json;

public partial class CustomDataLayerManager : Node2D
{
	// Separate exports for each custom data property layer
	[Export] public TileMapLayer SpeedModifierLayer { get; set; }
	[Export] public TileMapLayer CanBuildingLayer { get; set; }
	[Export] public TileMapLayer CanDestroyableObjectLayer { get; set; }
	[Export] public TileMapLayer CanPlantLayer { get; set; }
	[Export] public TileMapLayer CanEnemyLayer { get; set; }
	[Export] public TileMapLayer CanBridgeLayer { get; set; }
	[Export] public TileMapLayer CanFishingLayer { get; set; }
	[Export] public TileMapLayer ObjectTypePlacedLayer { get; set; }
	[Export] public TileMapLayer RegionLayer { get; set; }
	
	// Legacy support - will be populated from individual layer exports
	public TileMapLayer[] DataLayers { get; private set; } = Array.Empty<TileMapLayer>();

	private Dictionary<Vector2I, TileCustomData> _customDataCache = new();
	private Dictionary<Vector2I, BridgeOriginalTileData> _bridgeOriginalTiles = new();
	private Dictionary<Vector2I, BridgeTileData> _bridgeTiles = new();

	public override void _Ready()
	{
		// Populate DataLayers array from individual layer exports
		var layersList = new List<TileMapLayer>();

		if (SpeedModifierLayer != null) layersList.Add(SpeedModifierLayer);
		if (CanBuildingLayer != null) layersList.Add(CanBuildingLayer);
		if (CanDestroyableObjectLayer != null) layersList.Add(CanDestroyableObjectLayer);
		if (CanPlantLayer != null) layersList.Add(CanPlantLayer);
		if (CanEnemyLayer != null) layersList.Add(CanEnemyLayer);
		if (CanBridgeLayer != null) layersList.Add(CanBridgeLayer);
		if (CanFishingLayer != null) layersList.Add(CanFishingLayer);
		if (ObjectTypePlacedLayer != null) layersList.Add(ObjectTypePlacedLayer);
		if (RegionLayer != null) layersList.Add(RegionLayer);

		DataLayers = layersList.ToArray();

		// Initialize cache with existing data
		RefreshCache();
	}

	/// <summary>
	/// Model representing all custom data properties for a single tile
	/// </summary>
	public class TileCustomData
	{
		public float SpeedModifier { get; set; } = 1.0f;
		public bool CanBuilding { get; set; } = true;
		public bool CanDestroyableObject { get; set; } = true;
		public bool CanPlant { get; set; } = true;
		public bool CanEnemy { get; set; } = true;
		public bool CanBridge { get; set; } = true;
		public bool CanFishing { get; set; } = true;
		public ObjectTypePlaced ObjectTypePlaced { get; set; } = ObjectTypePlaced.None;
		public int Region { get; set; } = 0;

		// Layer information
		public int LayerIndex { get; set; } = 0;
		public Vector2I Position { get; set; }
	}

	/// <summary>
	/// Container for serializing all custom data
	/// </summary>
	public class CustomDataSaveData
	{
		public Dictionary<string, TileCustomData> TileData { get; set; } = new();
		public int LayerCount { get; set; } = 0;
		public string Version { get; set; } = "1.0";
	}

	/// <summary>
	/// Model representing original tile data for bridges
	/// </summary>
	public class BridgeOriginalTileData
	{
		public int SourceId { get; set; }
		public Vector2I AtlasCoords { get; set; }
	}

	/// <summary>
	/// Model representing bridge tile data for save/load
	/// </summary>
	public class BridgeTileData
	{
		public int SourceId { get; set; }
		public Vector2I AtlasCoords { get; set; }
		public Vector2I Position { get; set; }
	}

	/// <summary>
	/// Get custom data for a specific tile position
	/// </summary>
	public TileCustomData GetTileData(Vector2I position, int layerIndex = 0)
	{
		var key = GetCacheKey(position, layerIndex);
		if (_customDataCache.TryGetValue(key, out var cachedData))
		{
			return cachedData;
		}

		var customData = new TileCustomData
		{
			Position = position,
			LayerIndex = layerIndex
		};

		// Get data from each specific layer
		if (SpeedModifierLayer != null)
		{
			var tileData = SpeedModifierLayer.GetCellTileData(position);
			if (tileData != null)
				customData.SpeedModifier = tileData.GetCustomData("speedModifier").AsSingle();
		}

		if (CanBuildingLayer != null)
		{
			var tileData = CanBuildingLayer.GetCellTileData(position);
			if (tileData != null)
				customData.CanBuilding = tileData.GetCustomData("canBuilding").AsBool();
		}

		if (CanDestroyableObjectLayer != null)
		{
			var tileData = CanDestroyableObjectLayer.GetCellTileData(position);
			if (tileData != null)
				customData.CanDestroyableObject = tileData.GetCustomData("canDestroyableObject").AsBool();
		}

		if (CanPlantLayer != null)
		{
			var tileData = CanPlantLayer.GetCellTileData(position);
			if (tileData != null)
				customData.CanPlant = tileData.GetCustomData("canPlant").AsBool();
		}

		if (CanEnemyLayer != null)
		{
			var tileData = CanEnemyLayer.GetCellTileData(position);
			if (tileData != null)
				customData.CanEnemy = tileData.GetCustomData("canEnemy").AsBool();
		}

		if (CanBridgeLayer != null)
		{
			var tileData = CanBridgeLayer.GetCellTileData(position);
			if (tileData != null)
				customData.CanBridge = tileData.GetCustomData("canBridge").AsBool();
		}

		if (CanFishingLayer != null)
		{
			var tileData = CanFishingLayer.GetCellTileData(position);
			if (tileData != null)
				customData.CanFishing = tileData.GetCustomData("canFishing").AsBool();
		}

		if (ObjectTypePlacedLayer != null)
		{
			var tileData = ObjectTypePlacedLayer.GetCellTileData(position);
			if (tileData != null)
			{
				customData.ObjectTypePlaced = (ObjectTypePlaced)tileData.GetCustomData("objectTypePlaced").AsInt32();
			}
		}

		if (RegionLayer != null)
		{
			var tileData = RegionLayer.GetCellTileData(position);
			if (tileData != null)
				customData.Region = tileData.GetCustomData("region").AsInt32();
		}

		_customDataCache[key] = customData;
		return customData;
	}

	/// <summary>
	/// Set custom data for a specific tile position
	/// NOTE: This only updates the cache, not the actual TileMap, to avoid modifying shared TileData resources
	/// </summary>
	public void SetTileData(Vector2I position, TileCustomData data, int layerIndex = 0)
	{
		// Update cache only - don't modify TileMap to avoid shared resource issues
		data.Position = position;
		data.LayerIndex = layerIndex;
		var key = GetCacheKey(position, layerIndex);
		_customDataCache[key] = data;
	}

	/// <summary>
	/// Set specific custom data field for a tile
	/// NOTE: This only updates the cache, not the actual TileMap, to avoid modifying shared TileData resources
	/// </summary>
	public void SetTileProperty(Vector2I position, string propertyName, Variant value, int layerIndex = 0)
	{
		// Get or create tile data in cache
		var key = GetCacheKey(position, layerIndex);
		if (!_customDataCache.TryGetValue(key, out var tileData))
		{
			// If not in cache, get the base data from TileMap first
			tileData = GetTileData(position, layerIndex);
		}

		// Update the property in the cached data only
		switch (propertyName.ToLower())
		{
			case "speedmodifier":
				tileData.SpeedModifier = value.AsSingle();
				break;
			case "canbuilding":
				tileData.CanBuilding = value.AsBool();
				break;
			case "candestroyableobject":
				tileData.CanDestroyableObject = value.AsBool();
				break;
			case "canplant":
				tileData.CanPlant = value.AsBool();
				break;
			case "canenemy":
				tileData.CanEnemy = value.AsBool();
				break;
			case "canbridge":
				tileData.CanBridge = value.AsBool();
				break;
			case "canfishing":
				tileData.CanFishing = value.AsBool();
				break;
			case "objecttypeplaced":
				tileData.ObjectTypePlaced = (ObjectTypePlaced)value.AsInt32();
				break;
			case "region":
				tileData.Region = value.AsInt32();
				break;
			default:
				GD.PrintErr($"Unknown property: {propertyName}");
				return;
		}

		// Update cache with the modified data
		_customDataCache[key] = tileData;
	}

	/// <summary>
	/// Get specific custom data field value for a tile
	/// </summary>
	public Variant GetTileProperty(Vector2I position, string propertyName, int layerIndex = 0)
	{
		var tileData = GetTileData(position, layerIndex);

		return propertyName.ToLower() switch
		{
			"speedmodifier" => tileData.SpeedModifier,
			"canbuilding" => tileData.CanBuilding,
			"candestroyableobject" => tileData.CanDestroyableObject,
			"canplant" => tileData.CanPlant,
			"canenemy" => tileData.CanEnemy,
			"canbridge" => tileData.CanBridge,
			"canfishing" => tileData.CanFishing,
			"objecttypeplaced" => (int)tileData.ObjectTypePlaced,
			"region" => tileData.Region,
			_ => new Variant()
		};
	}

	/// <summary>
	/// Save all custom data to JSON string
	/// </summary>
	public string SaveToJson()
	{
		RefreshCache();

		var saveData = new CustomDataSaveData
		{
			LayerCount = DataLayers.Length,
			Version = "1.0"
		};

		// Only save tiles that have non-default values
		foreach (var kvp in _customDataCache)
		{
			var tileData = kvp.Value;

			// Check if this tile has any non-default values
			if (HasNonDefaultValues(tileData))
			{
				// Use the actual tile position from tileData, not the encoded cache key
				var key = $"{tileData.LayerIndex}_{tileData.Position.X}_{tileData.Position.Y}";
				saveData.TileData[key] = tileData;
			}
		}

		var options = new JsonSerializerOptions
		{
			WriteIndented = false,  // Remove all whitespace and formatting
			PropertyNamingPolicy = JsonNamingPolicy.CamelCase
		};

		return JsonSerializer.Serialize(saveData, options);
	}

	/// <summary>
	/// Check if a tile has any non-default values that should be saved
	/// Only save tiles that have actual objects placed (ObjectTypePlaced > 0) or other meaningful changes
	/// </summary>
	private bool HasNonDefaultValues(TileCustomData tileData)
	{
		// Always save tiles that have objects placed
		if (tileData.ObjectTypePlaced != ObjectTypePlaced.None)
		{
			return true;
		}

		// Save tiles with non-standard speed modifiers
		if (Math.Abs(tileData.SpeedModifier - 1.0f) > 0.01f)
		{
			return true;
		}

		// Don't save tiles just because they have a region assignment
		// Regions are assigned during world generation and are not "modifications"
		// Only save tiles that have actual objects or property changes

		// Save tiles with non-standard permissions (assuming most tiles allow everything by default)
		// Only save if a tile specifically disallows something
		if (!tileData.CanBuilding || !tileData.CanDestroyableObject || !tileData.CanPlant ||
			!tileData.CanEnemy || !tileData.CanBridge || !tileData.CanFishing)
		{
			return true;
		}

		// Don't save tiles with default values
		return false;
	}

	/// <summary>
	/// Load custom data from JSON string
	/// </summary>
	public void LoadFromJson(string jsonData)
	{
		try
		{
			var options = new JsonSerializerOptions
			{
				PropertyNamingPolicy = JsonNamingPolicy.CamelCase
			};

			var saveData = JsonSerializer.Deserialize<CustomDataSaveData>(jsonData, options);

			if (saveData == null)
			{
				GD.PrintErr("Failed to deserialize custom data");
				return;
			}

			_customDataCache.Clear();

			foreach (var kvp in saveData.TileData)
			{
				var parts = kvp.Key.Split('_');
				if (parts.Length == 3 &&
					int.TryParse(parts[0], out int layerIndex) &&
					int.TryParse(parts[1], out int x) &&
					int.TryParse(parts[2], out int y))
				{
					var position = new Vector2I(x, y);
					var tileData = kvp.Value;
					tileData.Position = position;
					tileData.LayerIndex = layerIndex;

					SetTileData(position, tileData, layerIndex);
				}
			}

			GD.Print($"Loaded custom data for {saveData.TileData.Count} tiles across {saveData.LayerCount} layers");
		}
		catch (Exception ex)
		{
			GD.PrintErr($"Error loading custom data from JSON: {ex.Message}");
		}
	}

	/// <summary>
	/// Refresh the cache with current TileMap data
	/// NOTE: Preserves existing cache entries to avoid losing runtime changes
	/// </summary>
	public void RefreshCache()
	{
		// Don't clear the cache - preserve runtime changes
		// Only add new tiles that aren't already cached
		for (int layerIndex = 0; layerIndex < DataLayers.Length; layerIndex++)
		{
			var tileMapLayer = DataLayers[layerIndex];
			var usedCells = tileMapLayer.GetUsedCells();

			foreach (var cell in usedCells)
			{
				var key = GetCacheKey(cell, layerIndex);
				if (!_customDataCache.ContainsKey(key))
				{
					GetTileData(cell, layerIndex); // This will populate the cache only if not already present
				}
			}
		}
	}

	/// <summary>
	/// Get all tiles with specific property values
	/// </summary>
	public List<TileCustomData> GetTilesWhere(Func<TileCustomData, bool> predicate)
	{
		RefreshCache();
		var result = new List<TileCustomData>();

		foreach (var tileData in _customDataCache.Values)
		{
			if (predicate(tileData))
			{
				result.Add(tileData);
			}
		}

		return result;
	}

	private Vector2I GetCacheKey(Vector2I position, int layerIndex)
	{
		// Encode layer index in the Y component's upper bits
		return new Vector2I(position.X, position.Y + (layerIndex << 16));
	}

	/// <summary>
	/// Clear all custom data for a specific tile across all layers
	/// </summary>
	public void ClearTileData(Vector2I position)
	{
		for (int i = 0; i < DataLayers.Length; i++)
		{
			var defaultData = new TileCustomData { Position = position, LayerIndex = i };
			SetTileData(position, defaultData, i);
		}
	}

	/// <summary>
	/// Get all custom data for a position across all layers
	/// </summary>
	public List<TileCustomData> GetAllLayerDataForPosition(Vector2I position)
	{
		var result = new List<TileCustomData>();
		for (int i = 0; i < DataLayers.Length; i++)
		{
			result.Add(GetTileData(position, i));
		}
		return result;
	}

	/// <summary>
	/// Check if a tile can have a building placed (checks canBuilding and objectTypePlaced)
	/// </summary>
	public bool CanPlaceBuilding(Vector2I position, int layerIndex = 0)
	{
		var tileData = GetTileData(position, layerIndex);
		return tileData.CanBuilding && tileData.ObjectTypePlaced == ObjectTypePlaced.None;
	}

	/// <summary>
	/// Check if a tile can have plants grown (checks canPlant and objectTypePlaced)
	/// </summary>
	public bool CanPlantOnTile(Vector2I position, int layerIndex = 0)
	{
		var tileData = GetTileData(position, layerIndex);
		return tileData.CanPlant && tileData.ObjectTypePlaced == ObjectTypePlaced.None;
	}

	/// <summary>
	/// Get movement speed modifier for a tile
	/// </summary>
	public float GetSpeedModifier(Vector2I position, int layerIndex = 0)
	{
		return GetTileData(position, layerIndex).SpeedModifier;
	}

	/// <summary>
	/// Mark a tile as occupied by an object
	/// </summary>
	public void SetObjectPlaced(Vector2I position, ObjectTypePlaced objectType, int layerIndex = 0)
	{
		SetTileProperty(position, "objectTypePlaced", (int)objectType, layerIndex);
	}

	/// <summary>
	/// Mark a tile as empty (remove object)
	/// </summary>
	public void ClearObjectPlaced(Vector2I position, int layerIndex = 0)
	{
		SetTileProperty(position, "objectTypePlaced", 0, layerIndex);
	}

	/// <summary>
	/// Clear all custom data cache - used for cleanup of corrupted data
	/// </summary>
	public void ClearAllCustomData()
	{
		_customDataCache.Clear();
		GD.Print("CustomDataLayerManager: Cleared all custom data cache");
	}

	/// <summary>
	/// Store original tile data for a bridge position
	/// </summary>
	public void SetBridgeOriginalTile(Vector2I position, int sourceId, Vector2I atlasCoords)
	{
		_bridgeOriginalTiles[position] = new BridgeOriginalTileData
		{
			SourceId = sourceId,
			AtlasCoords = atlasCoords
		};
	}

	/// <summary>
	/// Get original tile data for a bridge position
	/// </summary>
	public BridgeOriginalTileData GetBridgeOriginalTile(Vector2I position)
	{
		if (_bridgeOriginalTiles.TryGetValue(position, out var tileData))
		{
			return tileData;
		}
		return new BridgeOriginalTileData { SourceId = 0, AtlasCoords = Vector2I.Zero };
	}

	/// <summary>
	/// Clear original tile data for a bridge position
	/// </summary>
	public void ClearBridgeOriginalTile(Vector2I position)
	{
		_bridgeOriginalTiles.Remove(position);
	}

	/// <summary>
	/// Store bridge tile data for save/load
	/// </summary>
	public void SetBridgeTile(Vector2I position, int sourceId, Vector2I atlasCoords)
	{
		_bridgeTiles[position] = new BridgeTileData
		{
			Position = position,
			SourceId = sourceId,
			AtlasCoords = atlasCoords
		};
	}

	/// <summary>
	/// Remove bridge tile data
	/// </summary>
	public void ClearBridgeTile(Vector2I position)
	{
		_bridgeTiles.Remove(position);
	}

	/// <summary>
	/// Get all bridge tiles for saving
	/// </summary>
	public Dictionary<Vector2I, BridgeTileData> GetAllBridgeTiles()
	{
		return new Dictionary<Vector2I, BridgeTileData>(_bridgeTiles);
	}

	/// <summary>
	/// Restore bridge tiles from save data
	/// </summary>
	public void RestoreBridgeTiles(Dictionary<Vector2I, BridgeTileData> bridgeTiles)
	{
		_bridgeTiles = new Dictionary<Vector2I, BridgeTileData>(bridgeTiles);

		// Apply bridge tiles to the tilemap
		var bridgeLayer = GetNode<TileMapLayer>("/root/world/Layer2Floor_Bridge_SpeedModifier");
		if (bridgeLayer != null)
		{
			foreach (var kvp in _bridgeTiles)
			{
				var tileData = kvp.Value;
				bridgeLayer.SetCell(tileData.Position, tileData.SourceId, tileData.AtlasCoords);
			}
			GD.Print($"CustomDataLayerManager: Restored {_bridgeTiles.Count} bridge tiles");
		}
	}
}
