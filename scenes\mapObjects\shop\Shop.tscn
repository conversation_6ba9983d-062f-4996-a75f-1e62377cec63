[gd_scene load_steps=6 format=3 uid="uid://j6jd2tdkx7qu"]

[ext_resource type="Texture2D" uid="uid://khix3rrncf7a" path="res://resources/solaria/buildings/shop/shopBack.png" id="1_6m6m1"]
[ext_resource type="Texture2D" uid="uid://drculs1amfvkw" path="res://resources/solaria/buildings/shop/shopFront.png" id="2_4jmhs"]
[ext_resource type="Texture2D" uid="uid://c4pr73cegkkl0" path="res://resources/solaria/SpritePack/Base/Character/Idle.png" id="3_wt7j0"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_6m6m1"]
size = Vector2(126, 62)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_4jmhs"]
size = Vector2(131, 79)

[node name="Shop" type="Node2D"]
y_sort_enabled = true

[node name="Background" type="Sprite2D" parent="."]
z_index = -1
texture = ExtResource("1_6m6m1")

[node name="Foreground" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("2_4jmhs")

[node name="NPC2" type="Sprite2D" parent="."]
z_index = -1
y_sort_enabled = true
position = Vector2(-40, 27)
texture = ExtResource("3_wt7j0")
hframes = 6
vframes = 4
frame = 6

[node name="NPC1" type="Sprite2D" parent="."]
z_index = -1
position = Vector2(42, 26)
texture = ExtResource("3_wt7j0")
hframes = 6
vframes = 4
frame = 6

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 15)
shape = SubResource("RectangleShape2D_6m6m1")

[node name="PlayerCollider" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerCollider"]
position = Vector2(-0.5, 6.5)
shape = SubResource("RectangleShape2D_4jmhs")
