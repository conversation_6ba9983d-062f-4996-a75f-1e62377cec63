using Godot;
using System;

public partial class PlayerController : CharacterBody2D
{
	[Export]
	public float Speed = 100.0f;

	[Export]
	public CustomDataLayerManager CustomDataManager { get; set; }

	[Export]
	public int TileSize { get; set; } = 16;

	private AnimationPlayer _animationPlayer;
	private Sprite2D _sprite;
	private Sprite2D _toolSprite;
	private Sprite2D _directionIndicator;
	private string _currentAnimation = "idle_down";
	private ToolType _currentTool = ToolType.None;
	private bool _isUsingTool = false;
	private string _lastDirection = "down";
	private PackedScene _arrowScene;
	private float _baseSpeed;
	private float _currentSpeedModifier = 1.0f;
	private TilePlaceholder _tilePlaceholder;
	private PackedScene _placeholderScene;
	private bool _isMoving = false;
	private Area2D _playerDetector;
	private bool _movementEnabled = true;
	private float _lastBowShotTime = 0.0f;
	private float _bowCooldown = 1.5f;
	private float _lastBerryUseTime = 0.0f;
	private float _berryCooldown = 0.5f;
	private float _lastRabbitLegUseTime = 0.0f;
	private float _rabbitLegCooldown = 0.5f;
	private bool _swordSignalEmitted = false;
	private PlayerLight _playerLight;

	[Export]
	public float IndicatorDistance = 25.0f;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("PlayerAnimation");
		_sprite = GetNode<Sprite2D>("PlayerSprite");
		_toolSprite = GetNode<Sprite2D>("Tool");
		_directionIndicator = GetNode<Sprite2D>("DirectionIndicator");
		_playerDetector = GetNode<Area2D>("PlayerDetector");

		// Configure PlayerDetector collision layers
		if (_playerDetector != null)
		{
			// Layer 3: PlayerDetector layer
			// Mask 2: Detect DroppedResource layer
			_playerDetector.CollisionLayer = 4; // This Area2D is on layer 3 (bit 2 = 4)
			_playerDetector.CollisionMask = 2;   // This Area2D detects layer 2 (bit 1 = 2)
		}

		_baseSpeed = Speed;

		_toolSprite.Visible = false;
		_directionIndicator.Visible = false;

		_arrowScene = GD.Load<PackedScene>("res://scenes/arrow.tscn");

		LoadPlayerState();

		// Connect to movement control signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PlayerMovementEnabled += OnPlayerMovementEnabled;
			CommonSignals.Instance.UseResourceRequested += OnUseResourceRequested;
		}

		_placeholderScene = GD.Load<PackedScene>("res://scenes/TilePlaceholder.tscn");
		if (_placeholderScene != null)
		{
			_tilePlaceholder = _placeholderScene.Instantiate<TilePlaceholder>();
			_tilePlaceholder.TileSize = TileSize;

			GetParent().CallDeferred("add_child", _tilePlaceholder);

			_tilePlaceholder.GlobalPosition = GlobalPosition + new Vector2(TileSize, 0);
			_tilePlaceholder.Visible = true;

			GD.Print("TilePlaceholder created and will be added to scene");
		}
		else
		{
			GD.PrintErr("Failed to load TilePlaceholder scene");
		}

		// PlayerLight is now part of the scene, get reference to it
		_playerLight = GetNode<PlayerLight>("PlayerLight");
		if (_playerLight != null)
		{
			GD.Print("PlayerLight component found in scene");
		}
		else
		{
			GD.PrintErr("PlayerLight component not found in scene!");
		}
	}

	public override void _Process(double delta)
	{
		HandleToolInput();
		UpdateDirectionIndicator();
		UpdateSpeedFromTileData();
		UpdateTilePlaceholder();

		if (_isUsingTool || !_movementEnabled)
			return;

		Vector2 velocity = Vector2.Zero;

		if (Input.IsActionPressed("ui_up") || Input.IsKeyPressed(Key.W))
			velocity.Y -= 1;
		if (Input.IsActionPressed("ui_down") || Input.IsKeyPressed(Key.S))
			velocity.Y += 1;
		if (Input.IsActionPressed("ui_left") || Input.IsKeyPressed(Key.A))
			velocity.X -= 1;
		if (Input.IsActionPressed("ui_right") || Input.IsKeyPressed(Key.D))
			velocity.X += 1;

		if (velocity.Length() > 0)
			velocity = velocity.Normalized();

		_isMoving = velocity.Length() > 0;

		float statsSpeedModifier = PlayerStatsManager.Instance?.GetSpeedModifier() ?? 1.0f;
		Velocity = velocity * Speed * statsSpeedModifier;

		UpdateAnimation(velocity);
		Vector2 oldPosition = GlobalPosition;
		MoveAndSlide();

		// Update GameData if position changed
		if (GlobalPosition != oldPosition)
		{
			UpdatePlayerPositionInGameData();
		}
	}

	private void HandleToolInput()
	{
		if (Input.IsKeyPressed(Key.E))
		{
			if (!_isUsingTool)
			{
				var rm = ResourcesManager.Instance;
				if (rm != null)
				{
					var currentItem = rm.GetQuickSelectItem(GetCurrentSelectedSlot());
					if (currentItem != null && !currentItem.IsEmpty)
					{
						if (currentItem.IsTool)
						{
							if (_currentTool != ToolType.None)
							{
								UseTool();
							}
						}
						else
						{
							UseResource(currentItem.ResourceType);
						}
					}
					else if (_currentTool != ToolType.None)
					{
						UseTool();
					}
				}
			}
		}
	}

	public void SetCurrentTool(ToolType tool)
	{
		if (_currentTool != tool)
		{
			_currentTool = tool;
			UpdateSelectedToolInGameData();
			UpdateDirectionIndicatorVisibility();
		}
	}

	private void UseTool()
	{
		if (_currentTool == ToolType.None)
			return;

		// Check if player has energy to use tools
		if (PlayerStatsManager.Instance?.CurrentEnergy <= 0)
		{
			GD.Print("Cannot use tool: No energy remaining");
			return;
		}

		if (_currentTool == ToolType.Bow)
		{
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _lastBowShotTime < _bowCooldown)
			{
				return;
			}
		}

		_isUsingTool = true;
		_toolSprite.Visible = true;
		_swordSignalEmitted = false;

		UpdateDirectionIndicatorVisibility();

		string toolAnimation = "";
		switch (_currentTool)
		{
			case ToolType.Pickaxe:
				toolAnimation = "pickaxe_" + _lastDirection;
				break;
			case ToolType.Sword:
				toolAnimation = "sword_" + _lastDirection;
				break;
			case ToolType.Bow:
				toolAnimation = "bow_" + _lastDirection;
				break;
			case ToolType.Hammer:
				toolAnimation = "hammer_" + _lastDirection;
				break;
			case ToolType.Hoe:
				toolAnimation = "hoe_" + _lastDirection;
				break;
			case ToolType.WateringCan:
				toolAnimation = "wateringcan_" + _lastDirection;
				break;
		}

		if (!string.IsNullOrEmpty(toolAnimation))
		{
			_currentAnimation = toolAnimation;
			float animSpeedModifier = PlayerStatsManager.Instance?.GetAnimationSpeedModifier() ?? 1.0f;
			_animationPlayer.SpeedScale = animSpeedModifier;
			_animationPlayer.Play(_currentAnimation);
			_animationPlayer.AnimationFinished += OnToolAnimationFinished;

			if (_currentTool == ToolType.Sword)
			{
				float animationLength = (float)_animationPlayer.CurrentAnimationLength / animSpeedModifier;
				GetTree().CreateTimer(animationLength * 0.5f).Timeout += OnSwordMidAnimation;
			}
		}
		else
		{
			ExecuteToolAction();
			_isUsingTool = false;
			_toolSprite.Visible = false;
			UpdateDirectionIndicatorVisibility();
		}
	}

	private void OnToolAnimationFinished(StringName animName)
	{
		_animationPlayer.AnimationFinished -= OnToolAnimationFinished;

		if (_currentTool != ToolType.Sword)
		{
			ExecuteToolAction();
		}

		_isUsingTool = false;
		_toolSprite.Visible = false;

		UpdateDirectionIndicatorVisibility();

		_currentAnimation = "idle_" + _lastDirection;
		float animSpeedModifier = PlayerStatsManager.Instance?.GetAnimationSpeedModifier() ?? 1.0f;
		_animationPlayer.SpeedScale = animSpeedModifier;
		_animationPlayer.Play(_currentAnimation);
	}

	private void OnSwordMidAnimation()
	{
		if (_currentTool == ToolType.Sword && !_swordSignalEmitted)
		{
			_swordSignalEmitted = true;
			ExecuteToolAction();
		}
	}

	private void ExecuteToolAction()
	{
		Vector2I targetTile = GetLookingAtTile();

		CommonSignals.Instance?.EmitToolUsed();

		switch (_currentTool)
		{
			case ToolType.Bow:
				ShootArrow();
				_lastBowShotTime = Time.GetTicksMsec() / 1000.0f;
				break;
			case ToolType.Pickaxe:
				int pickaxeLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Pickaxe, out int level) ? level : 1;
				int damage = pickaxeLevel + 2;
				CommonSignals.Instance?.EmitPickaxeUsed(targetTile, damage);
				break;
			case ToolType.Hammer:
				int hammerLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Hammer, out int hammerLvl) ? hammerLvl : 1;
				int repairAmount = 2 + hammerLevel;
				CommonSignals.Instance?.EmitHammerUsed(targetTile, repairAmount);
				break;
			case ToolType.Hoe:
				CommonSignals.Instance?.EmitHoeUsed(targetTile);
				break;
			case ToolType.WateringCan:
				CommonSignals.Instance?.EmitWateringCanUsed(targetTile);
				break;
			case ToolType.Sword:
				Vector2 attackDirection = GetFacingDirectionVector();
				CommonSignals.Instance?.EmitSwordUsed(targetTile, GlobalPosition, attackDirection);
				break;
			default:
				GD.Print($"{_currentTool} action - not implemented yet");
				break;
		}
	}

	private void ShootArrow()
	{
		if (_arrowScene == null)
			return;

		Vector2 mousePos = GetGlobalMousePosition();
		Vector2 mouseDirection = (mousePos - GlobalPosition).Normalized();
		Vector2 shootDirection = GetValidShootingDirection(mouseDirection);

		Arrow arrow = _arrowScene.Instantiate<Arrow>();

		arrow.GlobalPosition = GlobalPosition + shootDirection * 10;

		arrow.Initialize(shootDirection);

		GetTree().CurrentScene.AddChild(arrow);
	}

	private void UseResource(ResourceType resourceType)
	{
		var rm = ResourcesManager.Instance;
		if (rm == null) return;

		if (resourceType == ResourceType.Berry)
		{
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _lastBerryUseTime < _berryCooldown)
			{
				return;
			}

			_lastBerryUseTime = currentTime;
			PlayerStatsManager.Instance?.ConsumeBerry();

			if (rm.HasResource(ResourceType.Berry, 1))
			{
				rm.RemoveResource(ResourceType.Berry, 1);
			}
		}
		else if (resourceType == ResourceType.RawRabbitLeg || resourceType == ResourceType.CookedRabbitLeg)
		{
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _lastRabbitLegUseTime < _rabbitLegCooldown)
			{
				return;
			}

			_lastRabbitLegUseTime = currentTime;

			if (resourceType == ResourceType.RawRabbitLeg)
			{
				PlayerStatsManager.Instance?.ConsumeRawRabbitLeg();
				if (rm.HasResource(ResourceType.RawRabbitLeg, 1))
				{
					rm.RemoveResource(ResourceType.RawRabbitLeg, 1);
				}
			}
			else if (resourceType == ResourceType.CookedRabbitLeg)
			{
				PlayerStatsManager.Instance?.ConsumeCookedRabbitLeg();
				if (rm.HasResource(ResourceType.CookedRabbitLeg, 1))
				{
					rm.RemoveResource(ResourceType.CookedRabbitLeg, 1);
				}
			}
		}
		else
		{
			GD.Print($"Using resource: {resourceType} - not implemented yet");
		}
	}

	private int GetCurrentSelectedSlot()
	{
		var selectedToolPanel = GetNode<SelectedToolPanel>("/root/world/SelectedToolPanel");
		if (selectedToolPanel != null)
		{
			return selectedToolPanel.GetCurrentSelectedSlot();
		}
		return 0;
	}

	private void UpdateDirectionIndicatorVisibility()
	{
		_directionIndicator.Visible = _currentTool == ToolType.Bow && !_isUsingTool;
	}

	private void UpdateDirectionIndicator()
	{
		if (!_directionIndicator.Visible)
			return;

		Vector2 mousePos = GetGlobalMousePosition();
		Vector2 mouseDirection = (mousePos - GlobalPosition).Normalized();

		Vector2 validDirection = GetValidShootingDirection(mouseDirection);

		Vector2 indicatorPos = GlobalPosition + validDirection * IndicatorDistance;
		_directionIndicator.GlobalPosition = indicatorPos;

		float angle = validDirection.Angle();
		_directionIndicator.Rotation = angle;
	}

	private Vector2 GetValidShootingDirection(Vector2 mouseDirection)
	{
		Vector2 facingDirection = GetFacingDirectionVector();

		float facingAngle = facingDirection.Angle();
		float mouseAngle = mouseDirection.Angle();

		float angleDiff = Mathf.AngleDifference(facingAngle, mouseAngle);

		float maxAngle = Mathf.Pi / 2;

		if (Mathf.Abs(angleDiff) <= maxAngle)
		{
			return mouseDirection;
		}
		else
		{
			float clampedAngle = facingAngle + Mathf.Sign(angleDiff) * maxAngle;
			return new Vector2(Mathf.Cos(clampedAngle), Mathf.Sin(clampedAngle));
		}
	}

	private Vector2 GetFacingDirectionVector()
	{
		return _lastDirection switch
		{
			"up" => Vector2.Up,
			"down" => Vector2.Down,
			"left" => Vector2.Left,
			"right" => Vector2.Right,
			_ => Vector2.Down
		};
	}

	private void UpdateAnimation(Vector2 velocity)
	{

		if (_isUsingTool)
			return;

		string newAnimation = _currentAnimation;

		if (velocity.Length() == 0)
		{
			if (_currentAnimation.StartsWith("walk_"))
			{
				newAnimation = "idle_" + _currentAnimation[5..];
			}
		}
		else
		{
			string newDirection;
			if (Math.Abs(velocity.X) >= Math.Abs(velocity.Y))
			{
				newDirection = velocity.X > 0 ? "right" : "left";
				newAnimation = velocity.X > 0 ? "walk_right" : "walk_left";
			}
			else
			{
				newDirection = velocity.Y > 0 ? "down" : "up";
				newAnimation = velocity.Y > 0 ? "walk_down" : "walk_up";
			}


			if (newDirection != _lastDirection)
			{
				_lastDirection = newDirection;
				UpdatePlayerDirectionInGameData();
			}
		}

		if (newAnimation != _currentAnimation)
		{
			_currentAnimation = newAnimation;
			float animSpeedModifier = PlayerStatsManager.Instance?.GetAnimationSpeedModifier() ?? 1.0f;
			_animationPlayer.SpeedScale = animSpeedModifier;
			_animationPlayer.Play(_currentAnimation);
		}
	}

	private void UpdateSpeedFromTileData()
	{
		if (CustomDataManager == null)
			return;

		Vector2I tilePosition = new(
			Mathf.FloorToInt(GlobalPosition.X / TileSize),
			Mathf.FloorToInt(GlobalPosition.Y / TileSize)
		);

		try
		{
			var tileData = CustomDataManager.GetTileData(tilePosition);

			if (Math.Abs(tileData.SpeedModifier - _currentSpeedModifier) > 0.001f)
			{
				if(Math.Abs(tileData.SpeedModifier) < 0.001) return;
				_currentSpeedModifier = tileData.SpeedModifier;
				Speed = _baseSpeed * _currentSpeedModifier;
			}
		}
		catch (Exception ex)
		{
			GD.PrintErr($"Error getting tile data at {tilePosition}: {ex.Message}");
			if (Math.Abs(_currentSpeedModifier - 1.0f) > 0.001f)
			{
				_currentSpeedModifier = 1.0f;
				Speed = _baseSpeed;
			}
		}
	}

	private void UpdateTilePlaceholder()
	{
		if (_tilePlaceholder == null)
		{
			return;
		}

		if (!ShouldShowPlaceholder())
		{
			_tilePlaceholder.Hide();
			return;
		}

		Vector2I playerTilePosition = new(
			Mathf.FloorToInt(GlobalPosition.X / TileSize),
			Mathf.FloorToInt(GlobalPosition.Y / TileSize)
		);

		Vector2I lookingAtTile = playerTilePosition + GetFacingDirectionTileOffset();

		_tilePlaceholder.ShowAtTile(lookingAtTile);

		float targetOpacity = _isMoving ? 0.2f : 0.7f;
		_tilePlaceholder.Modulate = new Color(1, 1, 1, targetOpacity);
	}

	private bool ShouldShowPlaceholder()
	{
		return _currentTool == ToolType.Pickaxe ||
			   _currentTool == ToolType.WateringCan ||
			   _currentTool == ToolType.Hoe ||
			   _currentTool == ToolType.Hammer;
	}

	private Vector2I GetFacingDirectionTileOffset()
	{
		return _lastDirection switch
		{
			"up" => Vector2I.Up,
			"down" => Vector2I.Down,
			"left" => Vector2I.Left,
			"right" => Vector2I.Right,
			_ => Vector2I.Down
		};
	}

	public Vector2I GetLookingAtTile()
	{
		Vector2I playerTilePosition = new(
			Mathf.FloorToInt(GlobalPosition.X / TileSize),
			Mathf.FloorToInt(GlobalPosition.Y / TileSize)
		);

		return playerTilePosition + GetFacingDirectionTileOffset();
	}

	public void HideTilePlaceholder()
	{
		_tilePlaceholder?.Hide();
	}

	public void ShowTilePlaceholder()
	{
		if (_tilePlaceholder != null)
		{
			Vector2I lookingAtTile = GetLookingAtTile();
			_tilePlaceholder.ShowAtTile(lookingAtTile);
		}
	}

	public string GetCurrentDirection() => _lastDirection;

	public ToolType GetCurrentTool() => _currentTool;

	public CustomDataLayerManager GetCustomDataManager() => CustomDataManager;

	private void LoadPlayerState()
	{
		var gameData = GameSaveData.Instance;

		Vector2 savedPosition = gameData.PlayerStats.Position;
		if (savedPosition != Vector2.Zero)
		{
			GlobalPosition = savedPosition;
		}

		string savedDirection = gameData.PlayerStats.LastDirection;
		if (!string.IsNullOrEmpty(savedDirection))
		{
			_lastDirection = savedDirection;
		}

		ToolType savedTool = gameData.PlayerStats.SelectedTool;
		SetCurrentTool(savedTool);

		if (CustomDataManager != null)
		{
			ResourcesManager.Instance?.LoadCustomLayerData(CustomDataManager);
		}
	}

	private void UpdatePlayerPositionInGameData()
	{
		GameSaveData.Instance.PlayerStats.Position = GlobalPosition;
	}

	private void UpdatePlayerDirectionInGameData()
	{
		GameSaveData.Instance.PlayerStats.LastDirection = _lastDirection;
	}

	private void UpdateSelectedToolInGameData()
	{
		GameSaveData.Instance.PlayerStats.SelectedTool = _currentTool;
	}

	private void OnPlayerMovementEnabled(bool enabled)
	{
		_movementEnabled = enabled;
		GD.Print($"Player movement {(enabled ? "enabled" : "disabled")}");
	}

	private void OnUseResourceRequested(ResourceType resourceType)
	{
		UseResource(resourceType);
		CommonSignals.Instance?.EmitResourceUsed(resourceType, true);
	}

	public override void _ExitTree()
	{
		// Disconnect from signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PlayerMovementEnabled -= OnPlayerMovementEnabled;
			CommonSignals.Instance.UseResourceRequested -= OnUseResourceRequested;
		}

		// Player state is automatically updated in GameData, no need to save explicitly
		base._ExitTree();
	}
}
